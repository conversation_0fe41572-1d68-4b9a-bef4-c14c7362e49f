// Initialize Checkout System
function initializeCheckoutSystem() {
    console.log('🔄 Initializing checkout system...');
    console.log('UnifiedCheckout available:', typeof UnifiedCheckout !== 'undefined');
    console.log('window.unifiedCheckout exists:', typeof window.unifiedCheckout !== 'undefined');

    // Initialize the unified checkout system
    if (typeof UnifiedCheckout !== 'undefined') {
        if (!window.unifiedCheckout) {
            window.unifiedCheckout = new UnifiedCheckout();
            console.log('✅ Checkout system initialized');
        } else {
            console.log('✅ Checkout system already exists');
        }
    } else {
        console.warn('⚠️ UnifiedCheckout class not found - checkout functionality may not work');
        console.log('Available globals:', Object.keys(window).filter(key => key.toLowerCase().includes('checkout')));
    }
}

// Call the initialization function when the script loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit to ensure all scripts are loaded
    setTimeout(initializeCheckoutSystem, 100);

    // Also try again after a longer delay as fallback
    setTimeout(() => {
        if (typeof window.unifiedCheckout === 'undefined') {
            console.log('🔄 Retrying checkout system initialization...');
            initializeCheckoutSystem();
        }
    }, 2000);
});