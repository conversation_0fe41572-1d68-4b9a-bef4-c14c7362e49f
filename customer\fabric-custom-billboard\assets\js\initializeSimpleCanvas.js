    // Simple canvas initialization that bypasses complex wrapper
    function initializeSimpleCanvas() {
        try {
            console.log('🔄 Checking Fabric.js availability...');
            if (typeof fabric === 'undefined') {
                throw new Error('Fabric.js not loaded');
            }
            console.log('✅ Fabric.js available (version: ' + fabric.version + ')');

            // Hide loading overlay
            const overlay = document.getElementById('canvasOverlay');
            if (overlay) {
                overlay.classList.add('hidden');
            }

            // Create simple canvas directly
            console.log('🔄 Creating canvas...');
            const canvas = new fabric.Canvas('fabricCanvas', {
                width: 800,
                height: 400,
                backgroundColor: '#ffffff',
                selection: true,
                preserveObjectStacking: true,
                enableRetinaScaling: true
            });

            // Add thick black border to canvas only
            const canvasElement = document.getElementById('fabricCanvas');

            if (canvasElement) {
                canvasElement.style.border = '4px solid #000000';
                canvasElement.style.borderRadius = '8px';
                canvasElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                canvasElement.style.display = 'block';
                canvasElement.style.margin = '0 auto';
                console.log('✅ Canvas styled directly without container');
            }

            if (!canvas) {
                throw new Error('Failed to create canvas');
            }

            console.log('✅ Canvas created successfully');

            // Setup responsive behavior - Fixed for desktop/mobile compatibility
            function resizeCanvas() {
                const canvasSection = document.querySelector('.canvas-section');
                if (!canvasSection || !canvas) return;

                // Get available space
                const sectionWidth = canvasSection.clientWidth - 40; // More padding for desktop
                const sectionHeight = canvasSection.clientHeight - 40;

                // Original canvas dimensions
                const originalWidth = 800;
                const originalHeight = 400;

                // Calculate scale to fit container while maintaining aspect ratio
                const scaleX = sectionWidth / originalWidth;
                const scaleY = sectionHeight / originalHeight;
                const scale = Math.min(scaleX, scaleY, 1); // Never scale up beyond 100%

                console.log('📐 Responsive canvas:', {
                    sectionSize: `${sectionWidth}x${sectionHeight}`,
                    scale: scale,
                    finalSize: `${originalWidth * scale}x${originalHeight * scale}`
                });

                // Apply scaling using CSS transform instead of Fabric.js zoom
                // This prevents object distortion issues
                const canvasWrapper = document.getElementById('canvasWrapper');

                if (canvasWrapper && scale < 1) {
                    // For smaller screens, use CSS transform scaling
                    canvas.setDimensions({
                        width: originalWidth,
                        height: originalHeight
                    });

                    canvasWrapper.style.transform = `scale(${scale})`;
                    canvasWrapper.style.transformOrigin = 'center center';
                    canvasWrapper.style.width = `${originalWidth}px`;
                    canvasWrapper.style.height = `${originalHeight}px`;

                    console.log('✅ Applied CSS transform scaling for responsive display');
                } else {
                    // For desktop/larger screens, keep original size
                    canvas.setDimensions({
                        width: originalWidth,
                        height: originalHeight
                    });

                    if (canvasWrapper) {
                        canvasWrapper.style.transform = 'scale(1)';
                        canvasWrapper.style.width = `${originalWidth}px`;
                        canvasWrapper.style.height = `${originalHeight}px`;
                    }

                    console.log('✅ Canvas displayed at original size for desktop');
                }

                canvas.renderAll();
            }

            window.addEventListener('resize', resizeCanvas);
            setTimeout(resizeCanvas, 100);

            // Setup responsive layout switching
            setupResponsiveLayout();

            // Store canvas globally for toolbar access
            window.billboardCanvas = canvas;

            console.log('🎉 Billboard Editor initialized successfully!');

            // Create simple canvas manager wrapper
            const canvasManager = {
                canvas: canvas, // Add direct canvas reference
                getCanvas: () => canvas,
                // Add core property for zoom/viewport methods
                core: {
                    fitToViewport: () => {
                        // Simple fit to viewport implementation
                        console.log('Fit to viewport requested');
                        // This could be enhanced with actual viewport fitting logic
                    },
                    resetZoom: () => {
                        // Simple zoom reset implementation
                        console.log('Zoom reset requested');
                        // This could be enhanced with actual zoom reset logic
                    }
                },
                addText: (text = 'Your Text Here') => {
                    // Use fabric.IText for interactive text editing
                    const textObj = new fabric.IText(text, {
                        left: canvas.width / 2,
                        top: canvas.height / 2,
                        fontFamily: 'Inter',
                        fontSize: 24,
                        fill: '#000000',
                        textAlign: 'center',
                        originX: 'center',
                        originY: 'center',
                        editable: true,
                        selectable: true,
                        evented: true,
                        hasControls: true,
                        hasBorders: true,
                        lockUniScaling: false,
                        // Enable text editing properties
                        isEditing: false,
                        editingBorderColor: '#2563eb',
                        cursorColor: '#2563eb',
                        cursorWidth: 2
                    });
                    canvas.add(textObj);
                    canvas.setActiveObject(textObj);
                    canvas.renderAll();
                    console.log('✅ Interactive text added - double-click to edit');
                    return textObj;
                },
                setBackgroundColor: (color) => {
                    canvas.setBackgroundColor(color, canvas.renderAll.bind(canvas));
                },
                setBackgroundImage: (url) => {
                    return new Promise((resolve, reject) => {
                        fabric.Image.fromURL(url, (img) => {
                            canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                                scaleX: canvas.width / img.width,
                                scaleY: canvas.height / img.height
                            });
                            resolve();
                        }, { crossOrigin: 'anonymous' });
                    });
                },
                clearBackground: () => {
                    canvas.setBackgroundImage(null, canvas.renderAll.bind(canvas));
                    canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
                },
                // Add missing clearCanvas method
                clearCanvas: () => {
                    const objects = canvas.getObjects().filter(obj => !obj.isBackground);
                    objects.forEach(obj => canvas.remove(obj));
                    canvas.discardActiveObject();
                    canvas.renderAll();
                    console.log('Canvas cleared - removed', objects.length, 'objects');
                },
                // Add missing addImage method
                addImage: (imageUrl) => {
                    return new Promise((resolve, reject) => {
                        fabric.Image.fromURL(imageUrl, (img) => {
                            if (!img) {
                                reject(new Error('Failed to load image'));
                                return;
                            }

                            // Position image in center
                            img.set({
                                left: canvas.width / 2,
                                top: canvas.height / 2,
                                originX: 'center',
                                originY: 'center',
                                selectable: true,
                                moveable: true,
                                hasControls: true,
                                hasBorders: true
                            });

                            // Scale image to fit canvas if too large
                            const maxWidth = canvas.width * 0.8;
                            const maxHeight = canvas.height * 0.8;

                            if (img.width > maxWidth || img.height > maxHeight) {
                                const scaleX = maxWidth / img.width;
                                const scaleY = maxHeight / img.height;
                                const scale = Math.min(scaleX, scaleY);

                                img.set({
                                    scaleX: scale,
                                    scaleY: scale
                                });
                            }

                            canvas.add(img);
                            canvas.setActiveObject(img);
                            canvas.renderAll();

                            console.log('Image added successfully');
                            resolve(img);

                        }, { crossOrigin: 'anonymous' });
                    });
                },
                // Add missing exportAsImage method
                exportAsImage: (options = {}) => {
                    const defaultOptions = {
                        format: 'png',
                        quality: 1,
                        multiplier: 1,
                        left: 0,
                        top: 0,
                        width: canvas.width,
                        height: canvas.height
                    };

                    const exportOptions = { ...defaultOptions, ...options };

                    console.log('Exporting canvas as image with options:', exportOptions);

                    // Use Fabric.js toDataURL with proper multiplier support
                    const fabricOptions = {
                        format: exportOptions.format,
                        quality: exportOptions.quality,
                        multiplier: exportOptions.multiplier,
                        left: exportOptions.left,
                        top: exportOptions.top,
                        width: exportOptions.width,
                        height: exportOptions.height
                    };

                    return canvas.toDataURL(fabricOptions);
                },
                // Event handling - support both canvas events and custom manager events
                on: (event, handler) => {
                    if (event === 'selection:changed' || event === 'object:changed') {
                        // For manager-specific events, use document event listeners
                        document.addEventListener(`canvas:${event}`, handler);
                    } else {
                        // For fabric canvas events, use canvas event listeners
                        canvas.on(event, handler);
                    }
                },
                // Event emitter for manager events
                emit: (eventName, data = {}) => {
                    const event = new CustomEvent(`canvas:${eventName}`, {
                        detail: { ...data, canvas: canvas, manager: canvasManager }
                    });
                    document.dispatchEvent(event);
                }
            };

            // Setup delete controls and object styling
            function setupDeleteFunctionality() {
                // Function to detect mobile/touch and update control sizes
                function updateControlSizes() {
                    const isMobile = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
                    const isSmallScreen = window.innerWidth <= 768;
                    const cornerSize = (isMobile || isSmallScreen) ? 24 : 12;

                    // Update all existing objects
                    canvas.getObjects().forEach(obj => {
                        if (!obj.isBackground) {
                            obj.set({ cornerSize: cornerSize });
                        }
                    });

                    // Update prototype for new objects
                    fabric.Object.prototype.set({
                        borderColor: '#2563eb',
                        cornerColor: '#ffffff',
                        cornerStrokeColor: '#2563eb',
                        cornerStyle: 'circle',
                        cornerSize: cornerSize,
                        transparentCorners: false,
                        hasControls: true,
                        hasBorders: true
                    });

                    canvas.renderAll();
                    return { isMobile, isSmallScreen };
                }

                // Initial setup
                const { isMobile, isSmallScreen } = updateControlSizes();

                // Update on window resize and orientation change
                window.addEventListener('resize', updateControlSizes);
                window.addEventListener('orientationchange', () => {
                    // Delay to allow orientation change to complete
                    setTimeout(updateControlSizes, 100);
                });

                // Add custom delete control (X button)
                function addDeleteControl(obj) {
                    // Mobile-responsive delete control sizing
                    const deleteControlSize = (isMobile || isSmallScreen) ? 44 : 24; // Larger on mobile
                    const offsetDistance = (isMobile || isSmallScreen) ? -12 : -8; // Adjust offset for larger size

                    obj.controls.deleteControl = new fabric.Control({
                        x: 0.5,
                        y: -0.5,
                        offsetY: offsetDistance,
                        offsetX: Math.abs(offsetDistance),
                        cursorStyle: 'pointer',
                        // Mobile-responsive sizing for better touch interaction
                        sizeX: deleteControlSize,
                        sizeY: deleteControlSize,
                        mouseUpHandler: function(eventData, transform) {
                            const target = transform.target;
                            canvas.remove(target);
                            canvas.renderAll();

                            // Hide text properties if text was deleted
                            const textProperties = document.getElementById('textProperties');
                            if (textProperties && (target.type === 'text' || target.type === 'i-text')) {
                                textProperties.style.display = 'none';
                            }

                            console.log('✅ Object deleted with X button');
                        },
                        render: function(ctx, left, top, styleOverride, fabricObject) {
                            // Mobile-responsive visual size
                            const size = (isMobile || isSmallScreen) ? 32 : 20;
                            const xSize = (isMobile || isSmallScreen) ? 10 : 6; // Larger X on mobile
                            const lineWidth = (isMobile || isSmallScreen) ? 4 : 3; // Thicker lines on mobile

                            ctx.save();
                            ctx.translate(left, top);
                            ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
                            ctx.fillStyle = '#ff0000';
                            ctx.strokeStyle = '#ffffff';
                            ctx.lineWidth = 2;

                            // Draw circle background
                            ctx.beginPath();
                            ctx.arc(0, 0, size/2, 0, 2 * Math.PI);
                            ctx.fill();
                            ctx.stroke();

                            // Draw X
                            ctx.strokeStyle = '#ffffff';
                            ctx.lineWidth = lineWidth;
                            ctx.beginPath();
                            ctx.moveTo(-xSize, -xSize);
                            ctx.lineTo(xSize, xSize);
                            ctx.moveTo(xSize, -xSize);
                            ctx.lineTo(-xSize, xSize);
                            ctx.stroke();

                            ctx.restore();
                        }
                    });
                }

                // Add delete control to all new objects
                canvas.on('object:added', function(e) {
                    if (e.target && !e.target.isBackground) {
                        addDeleteControl(e.target);
                    }
                });

                // Delete key functionality
                document.addEventListener('keydown', (e) => {
                    if ((e.key === 'Delete' || e.key === 'Backspace') && !e.target.matches('input, textarea')) {
                        const activeObjects = canvas.getActiveObjects();
                        if (activeObjects.length > 0) {
                            activeObjects.forEach(obj => {
                                canvas.remove(obj);
                            });
                            canvas.discardActiveObject();
                            canvas.renderAll();
                            console.log('✅ Objects deleted with keyboard');

                            // Hide text properties if text was deleted
                            const textProperties = document.getElementById('textProperties');
                            if (textProperties) {
                                textProperties.style.display = 'none';
                            }
                        }
                    }
                });

                // Right-click delete (but don't interfere with text editing)
                canvas.on('mouse:down', function(options) {
                    if (options.e.button === 2) { // Right click
                        options.e.preventDefault();
                        const activeObject = canvas.getActiveObject();
                        // Don't delete if text is in editing mode
                        if (activeObject && !activeObject.isEditing) {
                            canvas.remove(activeObject);
                            canvas.renderAll();
                            console.log('✅ Object deleted with right-click');

                            // Hide text properties
                            const textProperties = document.getElementById('textProperties');
                            if (textProperties) {
                                textProperties.style.display = 'none';
                            }
                        }
                    }
                });
            }

            // Initialize delete functionality
            setupDeleteFunctionality();

            // Initialize checkout system early
            initializeCheckoutSystem();

            // Setup canvas event listeners to emit manager events
            canvas.on('selection:created', (e) => {
                canvasManager.emit('selection:changed', { objects: e.selected || [] });
            });

            canvas.on('selection:updated', (e) => {
                canvasManager.emit('selection:changed', { objects: e.selected || [] });
            });

            canvas.on('selection:cleared', () => {
                canvasManager.emit('selection:changed', { objects: [] });
            });

            canvas.on('object:modified', (e) => {
                canvasManager.emit('object:changed', { object: e.target });
            });

            // Initialize mobile toolbar manager
            if (typeof MobileToolbarManager !== 'undefined') {
                window.toolbarManager = new MobileToolbarManager(canvasManager);
                console.log('✅ MobileToolbarManager initialized');

                // Listen for checkout event from toolbar
                document.addEventListener('toolbar:checkout:requested', async () => {
                    try {
                        console.log('🛒 Checkout requested from toolbar...');

                        // Deselect all canvas elements before checkout
                        if (canvas && canvas.discardActiveObject) {
                            canvas.discardActiveObject();
                            canvas.renderAll();
                            console.log('✅ Canvas elements deselected for checkout');
                        }

                        // Use clean export to avoid UI elements and reduce size
                        const dataURL = await exportCanvasClean(canvas);

                        // Prepare order data for checkout system
                        const orderData = {
                            imageData: dataURL,
                            billboardType: 'custom',
                            customerEmail: '', // Will be filled in checkout modal
                            customerName: '', // Will be filled in checkout modal
                            designData: {
                                canvasObjects: canvas.toJSON(),
                                canvasWidth: canvas.width,
                                canvasHeight: canvas.height,
                                timestamp: new Date().toISOString()
                            }
                        };

                        // Save order data to session storage for checkout modal
                        // Use localStorage as fallback if sessionStorage fails
                        try {
                            sessionStorage.setItem('billboardOrderData', JSON.stringify(orderData));
                        } catch (storageError) {
                            console.warn('SessionStorage failed, trying localStorage:', storageError);
                            // Try with compressed data
                            const compressedData = {
                                imageData: dataURL,
                                billboardType: 'custom',
                                designData: {
                                    canvasWidth: canvas.width,
                                    canvasHeight: canvas.height,
                                    timestamp: new Date().toISOString()
                                }
                            };
                            localStorage.setItem('billboardOrderData', JSON.stringify(compressedData));
                        }

                        // Initialize and open checkout modal
                        if (typeof window.unifiedCheckout === 'undefined') {
                            // 🔥 FIX: Check if UnifiedCheckout class is available
                            if (typeof UnifiedCheckout !== 'undefined') {
                                window.unifiedCheckout = new UnifiedCheckout();
                                console.log('✅ UnifiedCheckout initialized');
                            } else {
                                throw new Error('UnifiedCheckout class not loaded. Please refresh the page.');
                            }
                        }

                        await window.unifiedCheckout.open();

                        console.log('✅ Checkout modal opened successfully');

                    } catch (error) {
                        console.error('❌ Checkout failed:', error);
                        alert('Failed to start checkout process. Please try again.');
                    }
                });
            } else {
                // Fallback to basic toolbar
                initializeBasicToolbar(canvas);
                console.log('⚠️ Using basic toolbar fallback');
            }

        } catch (error) {
            console.error('❌ Initialization failed:', error);
            const loadingElement = document.getElementById('canvasLoading');
            if (loadingElement) {
                loadingElement.innerHTML = `
                    <div style="text-align: center; color: #dc2626;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <h3>Initialization Failed</h3>
                        <p>${error.message}</p>
                        <button onclick="window.location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #2563eb; color: white; border: none; border-radius: 0.5rem; cursor: pointer;">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                    </div>
                `;
            }
        }
    }

    /**
     * Export canvas with clean output (no UI elements) and optimized size
     */
    async function exportCanvasClean(canvas) {
        try {
            console.log('🧹 Starting clean canvas export...');

            // Fix any invalid textBaseline values before export
            canvas.forEachObject((obj) => {
                if (obj.type === 'text' || obj.type === 'i-text' || obj.type === 'textbox') {
                    // Fix invalid textBaseline values
                    if (obj.textBaseline === 'alphabetical') {
                        obj.textBaseline = 'alphabetic';
                        console.log('✅ Fixed invalid textBaseline value');
                    }
                }
            });

            // Prepare canvas for clean export
            const cleanupState = prepareCanvasForCleanExport(canvas);

            try {
                // Export with optimized settings for checkout (smaller file size)
                const dataURL = canvas.toDataURL({
                    format: 'png',
                    quality: 0.8, // Reduced quality for smaller file size
                    multiplier: 1.5, // Reduced multiplier for smaller file size
                    enableRetinaScaling: false // Disable for smaller file size
                });

                console.log('✅ Clean export completed');
                return dataURL;

            } finally {
                // Always restore canvas state
                restoreCanvasAfterExport(canvas, cleanupState);
            }

        } catch (error) {
            console.error('❌ Clean export failed:', error);

            // Fallback to basic export with error handling
            try {
                // Fix textBaseline issues first
                canvas.forEachObject((obj) => {
                    if (obj.type === 'text' || obj.type === 'i-text' || obj.type === 'textbox') {
                        if (obj.textBaseline === 'alphabetical') {
                            obj.textBaseline = 'alphabetic';
                        }
                    }
                });

                // Basic export with minimal settings
                return canvas.toDataURL({
                    format: 'png',
                    quality: 0.7,
                    multiplier: 1
                });

            } catch (fallbackError) {
                console.error('❌ Fallback export also failed:', fallbackError);
                throw new Error('Canvas export failed: ' + fallbackError.message);
            }
        }
    }

    /**
     * Prepare canvas for clean export by removing UI elements
     */
    function prepareCanvasForCleanExport(canvas) {
        const cleanupState = {
            activeObject: canvas.getActiveObject(),
            activeSelection: canvas.getActiveSelection ? canvas.getActiveSelection() : null, // 🔥 FIX: Check if method exists
            interactive: canvas.interactive,
            selection: canvas.selection,
            hoverCursor: canvas.hoverCursor,
            moveCursor: canvas.moveCursor,
            defaultCursor: canvas.defaultCursor,
            objectsState: []
        };

        // Store original object states and disable interactivity
        canvas.forEachObject((obj) => {
            const objState = {
                object: obj,
                selectable: obj.selectable,
                evented: obj.evented,
                hasControls: obj.hasControls,
                hasBorders: obj.hasBorders,
                hoverCursor: obj.hoverCursor,
                moveCursor: obj.moveCursor
            };
            cleanupState.objectsState.push(objState);

            // Disable all interactive features
            obj.selectable = false;
            obj.evented = false;
            obj.hasControls = false;
            obj.hasBorders = false;
            obj.hoverCursor = 'default';
            obj.moveCursor = 'default';
        });

        // Disable canvas interactivity
        canvas.interactive = false;
        canvas.selection = false;
        canvas.hoverCursor = 'default';
        canvas.moveCursor = 'default';
        canvas.defaultCursor = 'default';

        // Clear any active selections
        canvas.discardActiveObject();

        // Force canvas re-render to apply changes
        canvas.renderAll();

        console.log('✅ Canvas prepared for clean export (UI elements disabled)');
        return cleanupState;
    }

    /**
     * Restore canvas state after export
     */
    function restoreCanvasAfterExport(canvas, cleanupState) {
        try {
            // Restore canvas properties
            canvas.interactive = cleanupState.interactive;
            canvas.selection = cleanupState.selection;
            canvas.hoverCursor = cleanupState.hoverCursor;
            canvas.moveCursor = cleanupState.moveCursor;
            canvas.defaultCursor = cleanupState.defaultCursor;

            // Restore object states
            cleanupState.objectsState.forEach((objState) => {
                const obj = objState.object;
                obj.selectable = objState.selectable;
                obj.evented = objState.evented;
                obj.hasControls = objState.hasControls;
                obj.hasBorders = objState.hasBorders;
                obj.hoverCursor = objState.hoverCursor;
                obj.moveCursor = objState.moveCursor;
            });

            // Restore active selection if it existed
            if (cleanupState.activeObject) {
                canvas.setActiveObject(cleanupState.activeObject);
            }

            // Force canvas re-render to apply restored state
            canvas.renderAll();

            console.log('✅ Canvas state restored after export');

        } catch (error) {
            console.error('Error restoring canvas state:', error);
            // Force a basic restore
            canvas.interactive = true;
            canvas.selection = true;
            canvas.forEachObject((obj) => {
                obj.selectable = true;
                obj.evented = true;
                obj.hasControls = true;
                obj.hasBorders = true;
            });
            canvas.renderAll();
        }
    }
