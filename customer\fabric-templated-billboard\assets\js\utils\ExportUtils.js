/**
 * ExportUtils.js - High-quality export utilities for canvas content
 * Handles image generation, optimization, and download functionality
 */

class ExportUtils {
    constructor() {
        this.supportedFormats = ['png', 'jpeg', 'webp'];
        this.qualitySettings = {
            standard: 0.8,
            high: 0.95,
            maximum: 1.0,
            super: 1.0  // 🔥 NEW: Super quality setting
        };
        this.maxFileSize = 25 * 1024 * 1024; // 🔥 INCREASED to 25MB for super high quality
    }

    /**
     * Export canvas as high-quality image
     */
    static exportCanvas(canvas, options = {}) {
        const {
            format = 'png',
            quality = 'maximum', // 🔥 UPGRADED default to maximum quality
            scale = 8, // 🔥 INCREASED to 8x for ultra high quality (800x400 -> 6400x3200)
            filename = 'billboard-design',
            download = true
        } = options;

        return new Promise((resolve, reject) => {
            try {
                // Store original canvas state
                const originalZoom = canvas.getZoom();
                const originalWidth = canvas.getWidth();
                const originalHeight = canvas.getHeight();

                // Calculate export dimensions
                const exportWidth = originalWidth * scale;
                const exportHeight = originalHeight * scale;

                // Fix any invalid textBaseline values before export
                canvas.forEachObject((obj) => {
                    if (obj.type === 'text' || obj.type === 'i-text' || obj.type === 'textbox') {
                        if (obj.textBaseline === 'alphabetical') {
                            obj.textBaseline = 'alphabetic';
                            console.log('✅ Fixed invalid textBaseline value in templated export');
                        }
                    }
                });

                // Clean the canvas before export (remove UI elements)
                const cleanupState = ExportUtils.prepareCanvasForCleanExport(canvas);

                try {
                    // Generate ultra high-quality image data using multiplier approach
                    const qualityValue = ExportUtils.getQualityValue(quality);

                    console.log(`🎯 ExportUtils: Generating ${format.toUpperCase()} with ${scale}x multiplier`);
                    console.log(`   Canvas size: ${canvas.width}x${canvas.height}`);
                    console.log(`   Expected output: ${canvas.width * scale}x${canvas.height * scale}`);

                    const dataURL = canvas.toDataURL({
                        format: format,
                        quality: qualityValue,
                        multiplier: scale // Use multiplier for better quality
                    });

                    console.log(`✅ Generated image data URL length: ${dataURL.length} characters`);
                    console.log(`   Estimated file size: ~${Math.round(dataURL.length * 0.75 / 1024)} KB`);

                    return dataURL;

                } finally {
                    // Always restore canvas state
                    ExportUtils.restoreCanvasAfterExport(canvas, cleanupState);
                }
                canvas.renderAll();

                // Check file size
                const fileSize = ExportUtils.getDataURLSize(dataURL);
                console.log(`Export file size: ${(fileSize / 1024 / 1024).toFixed(2)}MB`);

                if (download) {
                    ExportUtils.downloadDataURL(dataURL, `${filename}.${format}`);
                }

                resolve({
                    dataURL,
                    fileSize,
                    format,
                    quality: qualityValue,
                    dimensions: { width: exportWidth, height: exportHeight }
                });

            } catch (error) {
                // Restore canvas state on error
                canvas.setDimensions({
                    width: originalWidth,
                    height: originalHeight
                });
                canvas.setZoom(originalZoom);
                canvas.renderAll();
                
                reject(error);
            }
        });
    }

    /**
     * Export with multiple quality options
     */
    static exportMultipleQualities(canvas, options = {}) {
        const {
            formats = ['png'],
            qualities = ['standard', 'high'],
            filename = 'billboard-design'
        } = options;

        const exports = [];

        formats.forEach(format => {
            qualities.forEach(quality => {
                const exportPromise = ExportUtils.exportCanvas(canvas, {
                    format,
                    quality,
                    filename: `${filename}-${quality}`,
                    download: false
                });
                exports.push(exportPromise);
            });
        });

        return Promise.all(exports);
    }

    /**
     * Get quality value from string
     */
    static getQualityValue(quality) {
        const qualityMap = {
            'low': 0.6,
            'standard': 0.8,
            'high': 0.95,
            'maximum': 1.0,
            'super': 1.0  // 🔥 NEW: Super quality setting
        };

        if (typeof quality === 'number') {
            return Math.max(0.1, Math.min(1.0, quality));
        }

        return qualityMap[quality] || qualityMap.maximum; // 🔥 Default to maximum instead of high
    }

    /**
     * Calculate data URL file size
     */
    static getDataURLSize(dataURL) {
        // Remove data URL prefix to get base64 data
        const base64 = dataURL.split(',')[1];
        
        // Calculate size (base64 is ~33% larger than binary)
        const binarySize = (base64.length * 3) / 4;
        
        // Account for padding
        const padding = base64.match(/=/g);
        return binarySize - (padding ? padding.length : 0);
    }

    /**
     * Optimize image quality based on file size
     */
    static async optimizeForFileSize(canvas, targetSize, options = {}) {
        const {
            format = 'jpeg',
            minQuality = 0.3,
            maxQuality = 1.0,
            filename = 'billboard-design'
        } = options;

        let quality = maxQuality;
        let result = null;

        // Binary search for optimal quality
        let low = minQuality;
        let high = maxQuality;

        while (high - low > 0.05) {
            quality = (low + high) / 2;
            
            result = await ExportUtils.exportCanvas(canvas, {
                format,
                quality,
                filename,
                download: false
            });

            if (result.fileSize <= targetSize) {
                low = quality;
            } else {
                high = quality;
            }
        }

        return result;
    }

    /**
     * Download data URL as file
     */
    static downloadDataURL(dataURL, filename) {
        const link = document.createElement('a');
        link.download = filename;
        link.href = dataURL;
        
        // Temporarily add to DOM for download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * Convert data URL to blob
     */
    static dataURLToBlob(dataURL) {
        const arr = dataURL.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        
        return new Blob([u8arr], { type: mime });
    }

    /**
     * Share image using Web Share API (mobile)
     */
    static async shareImage(dataURL, filename = 'billboard-design.png') {
        if (!navigator.share) {
            throw new Error('Web Share API not supported');
        }

        try {
            const blob = ExportUtils.dataURLToBlob(dataURL);
            const file = new File([blob], filename, { type: blob.type });

            await navigator.share({
                title: 'Billboard Design',
                text: 'Check out my billboard design!',
                files: [file]
            });

            return true;
        } catch (error) {
            console.error('Error sharing image:', error);
            throw error;
        }
    }

    /**
     * Copy image to clipboard
     */
    static async copyToClipboard(dataURL) {
        if (!navigator.clipboard || !navigator.clipboard.write) {
            throw new Error('Clipboard API not supported');
        }

        try {
            const blob = ExportUtils.dataURLToBlob(dataURL);
            const clipboardItem = new ClipboardItem({ [blob.type]: blob });
            
            await navigator.clipboard.write([clipboardItem]);
            return true;
        } catch (error) {
            console.error('Error copying to clipboard:', error);
            throw error;
        }
    }

    /**
     * Generate thumbnail from canvas
     */
    static generateThumbnail(canvas, options = {}) {
        const {
            width = 200,
            height = 100,
            format = 'jpeg',
            quality = 0.8
        } = options;

        // Calculate scale to fit thumbnail dimensions
        const canvasWidth = canvas.getWidth();
        const canvasHeight = canvas.getHeight();
        const scale = Math.min(width / canvasWidth, height / canvasHeight);

        return ExportUtils.exportCanvas(canvas, {
            format,
            quality,
            scale,
            download: false
        });
    }

    /**
     * Batch export multiple canvases
     */
    static async batchExport(canvases, options = {}) {
        const {
            format = 'png',
            quality = 'high',
            filenamePrefix = 'design'
        } = options;

        const results = [];

        for (let i = 0; i < canvases.length; i++) {
            try {
                const result = await ExportUtils.exportCanvas(canvases[i], {
                    format,
                    quality,
                    filename: `${filenamePrefix}-${i + 1}`,
                    download: false
                });
                results.push(result);
            } catch (error) {
                console.error(`Error exporting canvas ${i + 1}:`, error);
                results.push({ error: error.message });
            }
        }

        return results;
    }

    /**
     * Create print-ready export
     */
    static exportForPrint(canvas, options = {}) {
        const {
            dpi = 300,
            format = 'png',
            quality = 'maximum'
        } = options;

        // Calculate scale for print DPI (assuming 72 DPI base)
        const scale = dpi / 72;

        return ExportUtils.exportCanvas(canvas, {
            format,
            quality,
            scale,
            filename: 'billboard-print-ready',
            download: false
        });
    }

    /**
     * Validate export options
     */
    static validateExportOptions(options) {
        const errors = [];

        if (options.format && !this.supportedFormats.includes(options.format)) {
            errors.push(`Unsupported format: ${options.format}`);
        }

        if (options.quality && typeof options.quality === 'number') {
            if (options.quality < 0.1 || options.quality > 1.0) {
                errors.push('Quality must be between 0.1 and 1.0');
            }
        }

        if (options.scale && (options.scale < 0.1 || options.scale > 10)) {
            errors.push('Scale must be between 0.1 and 10');
        }

        return errors;
    }

    /**
     * Get export recommendations based on use case
     */
    static getExportRecommendations(useCase) {
        const recommendations = {
            web: {
                format: 'jpeg',
                quality: 'standard',
                scale: 1,
                description: 'Optimized for web display'
            },
            print: {
                format: 'png',
                quality: 'maximum',
                scale: 4,
                description: 'High resolution for printing'
            },
            social: {
                format: 'jpeg',
                quality: 'high',
                scale: 2,
                description: 'Optimized for social media sharing'
            },
            email: {
                format: 'jpeg',
                quality: 'standard',
                scale: 1.5,
                description: 'Balanced quality and file size for email'
            }
        };

        return recommendations[useCase] || recommendations.web;
    }

    /**
     * Monitor export progress (for large exports)
     */
    static createProgressMonitor(onProgress) {
        return {
            start: () => onProgress({ stage: 'starting', progress: 0 }),
            scaling: () => onProgress({ stage: 'scaling', progress: 25 }),
            rendering: () => onProgress({ stage: 'rendering', progress: 50 }),
            encoding: () => onProgress({ stage: 'encoding', progress: 75 }),
            complete: () => onProgress({ stage: 'complete', progress: 100 }),
            error: (error) => onProgress({ stage: 'error', progress: 0, error })
        };
    }

    /**
     * Estimate export time based on canvas size and options
     */
    static estimateExportTime(canvas, options = {}) {
        const { scale = 2, format = 'png' } = options;
        
        const pixels = canvas.getWidth() * canvas.getHeight() * scale * scale;
        const baseTime = pixels / 1000000; // Base time per megapixel
        
        // Format multipliers
        const formatMultipliers = {
            png: 1.5,
            jpeg: 1.0,
            webp: 1.2
        };
        
        const estimatedSeconds = baseTime * (formatMultipliers[format] || 1);
        
        return Math.max(1, Math.round(estimatedSeconds));
    }

    /**
     * Prepare canvas for clean export by removing UI elements
     */
    static prepareCanvasForCleanExport(canvas) {
        const cleanupState = {
            activeObject: canvas.getActiveObject(),
            activeSelection: canvas.getActiveSelection(),
            interactive: canvas.interactive,
            selection: canvas.selection,
            hoverCursor: canvas.hoverCursor,
            moveCursor: canvas.moveCursor,
            defaultCursor: canvas.defaultCursor,
            objectsState: []
        };

        // Store original object states and disable interactivity
        canvas.forEachObject((obj) => {
            const objState = {
                object: obj,
                selectable: obj.selectable,
                evented: obj.evented,
                hasControls: obj.hasControls,
                hasBorders: obj.hasBorders,
                hoverCursor: obj.hoverCursor,
                moveCursor: obj.moveCursor
            };
            cleanupState.objectsState.push(objState);

            // Disable all interactive features
            obj.selectable = false;
            obj.evented = false;
            obj.hasControls = false;
            obj.hasBorders = false;
            obj.hoverCursor = 'default';
            obj.moveCursor = 'default';
        });

        // Disable canvas interactivity
        canvas.interactive = false;
        canvas.selection = false;
        canvas.hoverCursor = 'default';
        canvas.moveCursor = 'default';
        canvas.defaultCursor = 'default';

        // Clear any active selections
        canvas.discardActiveObject();

        // Force canvas re-render to apply changes
        canvas.renderAll();

        console.log('✅ Canvas prepared for clean export (UI elements disabled)');
        return cleanupState;
    }

    /**
     * Restore canvas state after export
     */
    static restoreCanvasAfterExport(canvas, cleanupState) {
        try {
            // Restore canvas properties
            canvas.interactive = cleanupState.interactive;
            canvas.selection = cleanupState.selection;
            canvas.hoverCursor = cleanupState.hoverCursor;
            canvas.moveCursor = cleanupState.moveCursor;
            canvas.defaultCursor = cleanupState.defaultCursor;

            // Restore object states
            cleanupState.objectsState.forEach((objState) => {
                const obj = objState.object;
                obj.selectable = objState.selectable;
                obj.evented = objState.evented;
                obj.hasControls = objState.hasControls;
                obj.hasBorders = objState.hasBorders;
                obj.hoverCursor = objState.hoverCursor;
                obj.moveCursor = objState.moveCursor;
            });

            // Restore active selection if it existed
            if (cleanupState.activeObject) {
                canvas.setActiveObject(cleanupState.activeObject);
            }

            // Force canvas re-render to apply restored state
            canvas.renderAll();

            console.log('✅ Canvas state restored after export');

        } catch (error) {
            console.error('Error restoring canvas state:', error);
            // Force a basic restore
            canvas.interactive = true;
            canvas.selection = true;
            canvas.forEachObject((obj) => {
                obj.selectable = true;
                obj.evented = true;
                obj.hasControls = true;
                obj.hasBorders = true;
            });
            canvas.renderAll();
        }
    }
}

// Export for use in other modules
window.ExportUtils = ExportUtils;
