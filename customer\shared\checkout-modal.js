// ========================================
// UNIFIED CHECKOUT MODAL SYSTEM
// ========================================

class UnifiedCheckout {
    constructor() {
        this.isOpen = false;
        this.orderData = {};
        this.setupEventListeners();
        // Initialize with default values, will be updated when opened
        this.orderData = {
            billboardType: 'custom',
            selectedDates: 'Not selected',
            duration: 1,
            location: 'Standard Billboard Location',
            totalAmount: 75.00
        };
    }
    
    setupEventListeners() {
        // Close modal on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
        
        // Form validation
        const requiredCheckboxes = [
            'termsAgreement',
            'contentCompliance', 
            'businessAdCompliance',
            'designConfirmation',
            'refundPolicy'
        ];
        
        requiredCheckboxes.forEach(id => {
            const checkbox = document.getElementById(id);
            if (checkbox) {
                checkbox.addEventListener('change', () => {
                    this.validateForm();
                });
            }
        });
        
        // Customer info validation
        const requiredFields = ['checkoutCustomerName', 'checkoutCustomerEmail'];
        requiredFields.forEach(id => {
            const field = document.getElementById(id);
            if (field) {
                field.addEventListener('input', () => {
                    this.validateForm();
                });
            }
        });
    }
    
    async loadOrderData() {
        // Load data from localStorage
        this.orderData = {
            billboardType: this.getBillboardType(),
            selectedDates: this.getSelectedDates(),
            duration: this.calculateDuration(),
            location: this.getLocation(),
            totalAmount: await this.calculateTotalAmount()
        };
    }
    
    getBillboardType() {
        // Determine billboard type based on current page
        if (window.location.pathname.includes('templated-billboard')) {
            return 'Templated';
        } else if (window.location.pathname.includes('custom-billboard')) {
            return 'Custom';
        }
        return 'Custom';
    }
    
    getSelectedDates() {
        const dates = localStorage.getItem('selectedBillboardDates');
        if (dates) {
            try {
                const parsedDates = JSON.parse(dates);
                if (parsedDates.length > 0) {
                    const startDate = new Date(parsedDates[0]);
                    const endDate = new Date(parsedDates[parsedDates.length - 1]);
                    
                    const formatDate = (date) => {
                        return date.toLocaleDateString('en-US', { 
                            month: 'short', 
                            day: 'numeric', 
                            year: 'numeric' 
                        });
                    };
                    
                    if (parsedDates.length === 1) {
                        return formatDate(startDate);
                    } else {
                        return `${formatDate(startDate)} - ${formatDate(endDate)}`;
                    }
                }
            } catch (e) {
                console.error('Error parsing selected dates:', e);
            }
        }
        return 'Not selected';
    }
    
    calculateDuration() {
        const dates = localStorage.getItem('selectedBillboardDates');
        if (dates) {
            try {
                const parsedDates = JSON.parse(dates);
                return parsedDates.length;
            } catch (e) {
                console.error('Error calculating duration:', e);
            }
        }
        return 0;
    }
    
    getLocation() {
        // Get location from localStorage or use default
        const location = localStorage.getItem('billboardLocation');
        return location || 'Standard Billboard Location';
    }
    
    async calculateTotalAmount() {
        const duration = this.calculateDuration();
        const dailyRate = await this.getDailyRate();
        return duration * dailyRate;
    }

    async getDailyRate() {
        try {
            const response = await fetch('../shared/get-pricing.php');
            const data = await response.json();
            if (data.success) {
                return data.pricing.daily_rate;
            }
        } catch (error) {
            console.error('Failed to load pricing:', error);
        }
        // Fallback to default rate
        return 75.00;
    }
    
    async open() {
        await this.loadOrderData();
        this.updateModalContent();
        this.loadCustomerInfo();
        
        const modal = document.getElementById('unifiedCheckoutModal');
        if (modal) {
            modal.style.display = 'flex';
            this.isOpen = true;
            document.body.style.overflow = 'hidden';
            
            // Focus first input
            setTimeout(() => {
                const firstInput = modal.querySelector('input[type="text"]');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 100);
        }
    }
    
    close() {
        const modal = document.getElementById('unifiedCheckoutModal');
        if (modal) {
            modal.style.display = 'none';
            this.isOpen = false;
            document.body.style.overflow = '';
        }
    }
    
    updateModalContent() {
        // Update order summary
        // Ensure totalAmount is a valid number
        const totalAmount = typeof this.orderData.totalAmount === 'number' ? this.orderData.totalAmount : 0;

        const elements = {
            'checkoutBillboardType': this.orderData.billboardType,
            'checkoutDisplayDates': this.orderData.selectedDates,
            'checkoutDuration': `${this.orderData.duration} day${this.orderData.duration !== 1 ? 's' : ''}`,
            'checkoutLocation': this.orderData.location,
            'checkoutTotalAmount': `$${totalAmount.toFixed(2)}`
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }
    
    loadCustomerInfo() {
        // Pre-fill customer info if available
        const customerName = localStorage.getItem('customerName');
        const customerEmail = localStorage.getItem('customerEmail');
        const customerPhone = localStorage.getItem('customerPhone');
        
        if (customerName) {
            const nameField = document.getElementById('checkoutCustomerName');
            if (nameField) nameField.value = customerName;
        }
        
        if (customerEmail) {
            const emailField = document.getElementById('checkoutCustomerEmail');
            if (emailField) emailField.value = customerEmail;
        }
        
        if (customerPhone) {
            const phoneField = document.getElementById('checkoutCustomerPhone');
            if (phoneField) phoneField.value = customerPhone;
        }
    }
    
    validateForm() {
        const requiredCheckboxes = [
            'termsAgreement',
            'contentCompliance',
            'businessAdCompliance', 
            'designConfirmation',
            'refundPolicy'
        ];
        
        const requiredFields = [
            'checkoutCustomerName',
            'checkoutCustomerEmail'
        ];
        
        // Check all required checkboxes
        const allCheckboxesChecked = requiredCheckboxes.every(id => {
            const checkbox = document.getElementById(id);
            return checkbox && checkbox.checked;
        });
        
        // Check all required fields
        const allFieldsFilled = requiredFields.every(id => {
            const field = document.getElementById(id);
            return field && field.value.trim() !== '';
        });
        
        // Validate email format
        const emailField = document.getElementById('checkoutCustomerEmail');
        const emailValid = emailField && this.isValidEmail(emailField.value);
        
        const isValid = allCheckboxesChecked && allFieldsFilled && emailValid;
        
        // Update proceed button
        const proceedBtn = document.getElementById('proceedToPaymentBtn');
        if (proceedBtn) {
            proceedBtn.disabled = !isValid;
        }
        
        return isValid;
    }
    
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    async proceedToPayment() {
        if (!this.validateForm()) {
            alert('Please complete all required fields and accept all terms before proceeding.');
            return;
        }

        // Save customer information
        this.saveCustomerInfo();

        // Capture high-quality design data for post-payment processing
        await this.captureDesignDataForPayment();

        // Save order data for payment processing
        this.saveOrderForPayment();

        // Show loading state
        const proceedBtn = document.getElementById('proceedToPaymentBtn');
        if (proceedBtn) {
            proceedBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Initializing Payment...';
            proceedBtn.disabled = true;
        }

        // Initialize Stripe payment
        this.initializeStripePayment();
    }

    async initializeStripePayment() {
        try {
            // Prepare order data for payment intent
            const selectedDatesString = localStorage.getItem('selectedBillboardDates');
            let selectedDates = selectedDatesString ? JSON.parse(selectedDatesString) : [];

            // For testing purposes, if no dates are selected, create a default date range
            if (!selectedDates || selectedDates.length === 0) {
                const today = new Date();
                const tomorrow = new Date(today);
                tomorrow.setDate(tomorrow.getDate() + 1);

                selectedDates = [
                    today.toISOString().split('T')[0],
                    tomorrow.toISOString().split('T')[0]
                ];

                console.warn('No dates selected, using default dates for testing:', selectedDates);
            }

            // Include design data for post-payment processing
            const paymentDesignData = localStorage.getItem('paymentDesignData');
            const checkoutOrderData = localStorage.getItem('checkoutOrderData');

            console.log('🔍 Payment design data from localStorage:', paymentDesignData ? 'Found' : 'NOT FOUND');
            if (paymentDesignData) {
                console.log('📊 Payment design data size:', paymentDesignData.length, 'characters');
            } else {
                console.warn('⚠️ No payment design data found in localStorage - this will cause placeholder image generation!');
            }

            const orderData = {
                selectedDates: selectedDates,
                billboardType: this.orderData.billboardType.toLowerCase(),
                designData: paymentDesignData ? JSON.parse(paymentDesignData) : null,
                customerData: checkoutOrderData ? JSON.parse(checkoutOrderData) : null,
                // Include design data directly in payment metadata for easier access
                designDataString: paymentDesignData || null
            };

            console.log('Sending order data to payment intent:', orderData);

            // Create payment intent
            const response = await fetch('../shared/payment/create-payment-intent.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(orderData)
            });

            // Check if response is ok
            if (!response.ok) {
                const errorText = await response.text();
                console.error('Payment intent creation failed:', response.status, errorText);
                throw new Error(`Server error: ${response.status}. Response: ${errorText.substring(0, 200)}`);
            }

            const responseText = await response.text();
            console.log('Payment intent response:', responseText);

            let result;
            try {
                result = JSON.parse(responseText);
            } catch (parseError) {
                console.error('Failed to parse payment response as JSON:', responseText);
                throw new Error(`Invalid JSON response from server. Response: ${responseText.substring(0, 200)}`);
            }

            if (!result.success) {
                throw new Error(result.error || 'Failed to initialize payment');
            }

            // Store payment intent data
            localStorage.setItem('paymentIntentId', result.paymentIntentId);
            localStorage.setItem('clientSecret', result.clientSecret);

            // Close modal and redirect to Stripe payment page
            this.close();

            // Redirect to Stripe payment page
            window.location.href = `../shared/payment/stripe-checkout.php?client_secret=${result.clientSecret}`;

        } catch (error) {
            console.error('Payment initialization error:', error);

            // Reset button state
            const proceedBtn = document.getElementById('proceedToPaymentBtn');
            if (proceedBtn) {
                proceedBtn.innerHTML = '<i class="fas fa-credit-card"></i> Proceed to Payment';
                proceedBtn.disabled = false;
            }

            alert('Failed to initialize payment: ' + error.message);
        }
    }
    
    saveCustomerInfo() {
        const customerName = document.getElementById('checkoutCustomerName').value;
        const customerEmail = document.getElementById('checkoutCustomerEmail').value;
        const customerPhone = document.getElementById('checkoutCustomerPhone').value;
        const emailCopy = document.getElementById('emailCopy').checked;

        // Save to order data manager
        if (window.orderDataManager) {
            window.orderDataManager.setCustomerInfo(customerName, customerEmail, customerPhone);
        }

        // Save to session for server-side access
        this.saveCustomerInfoToSession(customerName, customerEmail, customerPhone);

        // Keep legacy localStorage for backward compatibility
        localStorage.setItem('customerName', customerName);
        localStorage.setItem('customerEmail', customerEmail);
        localStorage.setItem('customerPhone', customerPhone);
        localStorage.setItem('emailCopyRequested', emailCopy);
    }

    // Save customer info to session via AJAX
    async saveCustomerInfoToSession(customerName, customerEmail, customerPhone) {
        try {
            await fetch('../shared/save-customer-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    customer_name: customerName,
                    customer_email: customerEmail,
                    customer_phone: customerPhone
                })
            });
        } catch (error) {
            console.error('Failed to save customer info to session:', error);
        }
    }
    
    /**
     * Capture high-quality design data for post-payment image generation
     */
    async captureDesignDataForPayment() {
        try {
            let designData = {};
            let canvasImageData = null;

            // Capture design data based on billboard type
            if (this.orderData.billboardType.toLowerCase() === 'custom') {
                designData = await this.captureCustomBillboardData();
            } else {
                designData = await this.captureTemplatedBillboardData();
            }

            // Capture high-quality canvas image data for fallback
            canvasImageData = await this.captureCanvasImageData();

            // Store design data for post-payment processing
            const paymentDesignData = {
                billboardType: this.orderData.billboardType.toLowerCase(),
                designData: designData,
                canvasImageData: canvasImageData,
                capturedAt: new Date().toISOString(),
                qualitySettings: {
                    pixelRatio: Math.max(8, window.devicePixelRatio || 1),
                    quality: 1.0, // Maximum quality
                    format: 'png',
                    dimensions: { width: 800, height: 400 },
                    superHighResolutionDimensions: { width: 6400, height: 3200 }, // 🔥 SUPER HIGH target
                    ultraHighResolutionDimensions: { width: 9600, height: 4800 }  // 🔥 ULTRA HIGH target (3200x1600 base × 3x multiplier)
                }
            };

            localStorage.setItem('paymentDesignData', JSON.stringify(paymentDesignData));
            console.log('✅ Design data captured and stored in localStorage for payment:', paymentDesignData);
            console.log('📊 Stored design data size:', JSON.stringify(paymentDesignData).length, 'characters');

        } catch (error) {
            console.error('Failed to capture design data:', error);
            // Continue with payment even if design capture fails
        }
    }

    /**
     * Capture custom billboard design data
     */
    async captureCustomBillboardData() {
        const designData = {};

        // Try to get data from CF7 editor
        if (window.cf7Editors && window.cf7Editors[0]) {
            const editor = window.cf7Editors[0];

            // Capture canvas elements
            designData.canvasElements = this.captureCanvasElements();

            // Capture current settings
            designData.settings = {
                backgroundColor: editor.backgroundColor || '#ffffff',
                backgroundImage: editor.backgroundImage || null,
                canvasDimensions: { width: 800, height: 400 }
            };

            // Capture text elements
            designData.textElements = this.captureTextElements();

            // Capture image elements
            designData.imageElements = this.captureImageElements();
        }

        return designData;
    }

    /**
     * Capture templated billboard design data
     */
    async captureTemplatedBillboardData() {
        const designData = {};

        // Get template information
        const templateId = localStorage.getItem('selectedTemplateId');
        const templateCategory = localStorage.getItem('selectedTemplateCategory');

        designData.templateId = templateId;
        designData.templateCategory = templateCategory;

        // Capture customizations
        designData.customizations = this.captureTemplateCustomizations();

        return designData;
    }

    saveOrderForPayment() {
        const orderData = {
            ...this.orderData,
            customerName: document.getElementById('checkoutCustomerName').value,
            customerEmail: document.getElementById('checkoutCustomerEmail').value,
            customerPhone: document.getElementById('checkoutCustomerPhone').value,
            emailCopyRequested: document.getElementById('emailCopy').checked,
            termsAccepted: true,
            termsAcceptedAt: new Date().toISOString()
        };

        localStorage.setItem('checkoutOrderData', JSON.stringify(orderData));
    }

    /**
     * Capture canvas elements for custom billboards
     */
    captureCanvasElements() {
        const elements = [];
        const canvas = document.querySelector('.cf7-canvas, .billboard-canvas');

        if (canvas) {
            // Capture all child elements
            const children = canvas.querySelectorAll('.cf7-element, .text-element, .image-element');
            children.forEach(element => {
                const elementData = {
                    type: element.classList.contains('cf7-text') || element.classList.contains('text-element') ? 'text' : 'image',
                    id: element.id || '',
                    className: element.className,
                    style: element.getAttribute('style') || '',
                    content: element.textContent || element.innerHTML,
                    position: {
                        left: element.style.left,
                        top: element.style.top,
                        width: element.style.width,
                        height: element.style.height
                    }
                };
                elements.push(elementData);
            });
        }

        return elements;
    }

    /**
     * Capture text elements specifically
     */
    captureTextElements() {
        const textElements = [];
        const textNodes = document.querySelectorAll('.cf7-text, .text-element');

        textNodes.forEach(element => {
            textElements.push({
                id: element.id,
                content: element.textContent,
                style: element.getAttribute('style'),
                fontSize: element.style.fontSize,
                fontFamily: element.style.fontFamily,
                color: element.style.color,
                position: {
                    left: element.style.left,
                    top: element.style.top
                }
            });
        });

        return textElements;
    }

    /**
     * Capture image elements specifically
     */
    captureImageElements() {
        const imageElements = [];
        const imageNodes = document.querySelectorAll('.cf7-image, .image-element img');

        imageNodes.forEach(element => {
            imageElements.push({
                id: element.id,
                src: element.src,
                alt: element.alt,
                style: element.getAttribute('style'),
                position: {
                    left: element.style.left,
                    top: element.style.top,
                    width: element.style.width,
                    height: element.style.height
                }
            });
        });

        return imageElements;
    }

    /**
     * Capture template customizations for templated billboards
     */
    captureTemplateCustomizations() {
        const customizations = {};

        // Capture text customizations
        const textElements = document.querySelectorAll('.text-element');
        textElements.forEach((element, index) => {
            customizations[`text_${index}`] = {
                content: element.textContent,
                style: element.getAttribute('style')
            };
        });

        // Capture image customizations
        const imageElements = document.querySelectorAll('.image-element img');
        imageElements.forEach((element, index) => {
            customizations[`image_${index}`] = {
                src: element.src,
                style: element.getAttribute('style')
            };
        });

        return customizations;
    }

    /**
     * Capture high-quality canvas image data with Fabric.js support
     */
    async captureCanvasImageData() {
        try {
            // First try to find Fabric.js canvas
            const fabricCanvas = this.findFabricCanvas();
            if (fabricCanvas) {
                console.log('Found Fabric.js canvas, using high-quality export');
                return await this.captureFabricCanvasData(fabricCanvas);
            }

            // Fallback to HTML canvas capture
            const canvas = document.querySelector('.cf7-canvas, .billboard-canvas, #billboard-canvas, canvas');
            if (!canvas) {
                console.warn('No canvas found for image capture');
                return null;
            }

            // Use high-quality image generator if available
            if (window.HighQualityImageGenerator) {
                const generator = new window.HighQualityImageGenerator();
                const imageData = await generator.generateImageData(canvas);
                return imageData;
            }

            // Fallback to basic canvas capture
            return await this.basicCanvasCapture(canvas);

        } catch (error) {
            console.error('Failed to capture canvas image:', error);
            return null;
        }
    }

    /**
     * Find active Fabric.js canvas instance
     */
    findFabricCanvas() {
        console.log('🔍 Searching for Fabric.js canvas...');

        // Check for global canvas instances
        if (window.canvas && window.canvas.toDataURL) {
            console.log('✅ Found global window.canvas');
            return window.canvas;
        }

        // Check for canvas in CF7 editors
        if (window.cf7Editors && window.cf7Editors.length > 0) {
            for (const editor of window.cf7Editors) {
                if (editor.canvas && editor.canvas.toDataURL) {
                    console.log('✅ Found canvas in CF7 editor');
                    return editor.canvas;
                }
            }
        }

        // Check for canvas in billboard editor modules
        if (window.billboardEditor && window.billboardEditor.modules) {
            const canvasManager = window.billboardEditor.modules.canvasManager;
            if (canvasManager && canvasManager.getCanvas) {
                const canvas = canvasManager.getCanvas();
                if (canvas && canvas.toDataURL) {
                    console.log('✅ Found canvas in billboard editor modules');
                    return canvas;
                }
            }
        }

        // Check for templated billboard canvas
        if (window.canvasManager && window.canvasManager.canvas) {
            console.log('✅ Found canvas in canvasManager');
            return window.canvasManager.canvas;
        }

        // Check all Fabric.js instances and match with canvas elements
        if (window.fabric && window.fabric.Canvas && window.fabric.Canvas.getInstances) {
            const fabricInstances = window.fabric.Canvas.getInstances();
            console.log(`🔍 Found ${fabricInstances.length} Fabric.js instances`);

            // Check common canvas IDs
            const canvasIds = ['fabricCanvas', 'billboard-canvas', 'canvas'];

            for (const canvasId of canvasIds) {
                const canvasElement = document.getElementById(canvasId);
                if (canvasElement) {
                    console.log(`🔍 Found canvas element with ID: ${canvasId}`);

                    // Find matching Fabric instance
                    for (let i = 0; i < fabricInstances.length; i++) {
                        const fabricCanvas = fabricInstances[i];
                        if (fabricCanvas.lowerCanvasEl === canvasElement ||
                            fabricCanvas.upperCanvasEl === canvasElement ||
                            fabricCanvas.getElement() === canvasElement) {
                            console.log(`✅ Found matching Fabric.js canvas for ${canvasId}`);
                            return fabricCanvas;
                        }
                    }
                }
            }

            // If no specific match, return the first available Fabric instance
            if (fabricInstances.length > 0) {
                console.log('✅ Using first available Fabric.js instance');
                return fabricInstances[0];
            }
        }

        console.log('❌ No Fabric.js canvas found');
        return null;
    }

    /**
     * Capture high-quality data from Fabric.js canvas using the specialized exporter
     */
    async captureFabricCanvasData(fabricCanvas) {
        try {
            console.log('Capturing Fabric.js canvas data using specialized exporter');

            // Use the dedicated Fabric.js canvas exporter for checkout
            if (window.fabricCanvasExporter) {
                const exportResult = await window.fabricCanvasExporter.exportForCheckout(fabricCanvas);

                return {
                    imageData: exportResult.imageData,
                    canvasJSON: exportResult.canvasJSON,
                    dimensions: exportResult.dimensions,
                    captureMethod: exportResult.captureMethod,
                    qualityLevel: exportResult.qualityLevel,
                    fileSize: exportResult.fileSize,
                    timestamp: exportResult.timestamp
                };
            }

            // Fallback to direct export if exporter not available
            console.warn('Fabric canvas exporter not available, using ULTRA HIGH RESOLUTION direct export');

            // 🔥 ULTRA HIGH RESOLUTION FALLBACK EXPORT - Create new canvas
            console.log('🚀 Creating ULTRA HIGH RESOLUTION temporary canvas...');

            // 🔥 ULTRA HIGH RESOLUTION SETTINGS
            const ultraWidth = 3200;   // 🔥 ULTRA HIGH base width
            const ultraHeight = 1600;  // 🔥 ULTRA HIGH base height
            const ultraMultiplier = 3; // 🔥 3x multiplier = 9600x4800 final resolution

            console.log(`🎯 ULTRA HIGH RESOLUTION TARGET: ${ultraWidth * ultraMultiplier}x${ultraHeight * ultraMultiplier} pixels`);

            // 🔥 CREATE COMPLETELY NEW CANVAS at ultra high resolution
            const tempCanvasElement = document.createElement('canvas');
            tempCanvasElement.width = ultraWidth;
            tempCanvasElement.height = ultraHeight;
            tempCanvasElement.style.position = 'absolute';
            tempCanvasElement.style.left = '-9999px';
            tempCanvasElement.style.top = '-9999px';
            document.body.appendChild(tempCanvasElement);

            const ultraCanvas = new fabric.Canvas(tempCanvasElement, {
                width: ultraWidth,
                height: ultraHeight,
                enableRetinaScaling: false
            });

            let highQualityImage;
            let canvasJSON;

            try {
                // 🔥 COPY ALL OBJECTS from original canvas to ultra high resolution canvas
                canvasJSON = fabricCanvas.toJSON(['selectable', 'evented', 'id', 'name']);

                // Calculate scaling factors
                const scaleX = ultraWidth / fabricCanvas.width;
                const scaleY = ultraHeight / fabricCanvas.height;

                console.log(`📐 Scaling objects: ${scaleX.toFixed(2)}x horizontal, ${scaleY.toFixed(2)}x vertical`);

                // Load the canvas data into ultra high resolution canvas
                await new Promise((resolve, reject) => {
                    ultraCanvas.loadFromJSON(canvasJSON, () => {
                        // Scale all objects to ultra high resolution
                        ultraCanvas.getObjects().forEach(obj => {
                            obj.scaleX = (obj.scaleX || 1) * scaleX;
                            obj.scaleY = (obj.scaleY || 1) * scaleY;
                            obj.left = obj.left * scaleX;
                            obj.top = obj.top * scaleY;
                            obj.setCoords();
                        });

                        ultraCanvas.renderAll();
                        resolve();
                    }, reject);
                });

                // 🔥 EXPORT at ultra high quality
                highQualityImage = ultraCanvas.toDataURL({
                    format: 'png',
                    quality: 1.0,
                    multiplier: ultraMultiplier,
                    enableRetinaScaling: true
                });

                // Verify output resolution
                const img = new Image();
                img.onload = function() {
                    console.log(`🎉 ULTRA HIGH RESOLUTION ACHIEVED: ${img.width}x${img.height} pixels`);
                    const fileSizeKB = Math.round(highQualityImage.length * 0.75 / 1024);
                    console.log(`📊 File size: ${fileSizeKB}KB (${(fileSizeKB/1024).toFixed(2)}MB)`);
                };
                img.src = highQualityImage;

            } finally {
                // 🔥 CLEANUP: Remove temporary canvas
                ultraCanvas.dispose();
                document.body.removeChild(tempCanvasElement);
                console.log('✅ Temporary ultra high resolution canvas cleaned up');
            }

                // Also capture the canvas JSON for reconstruction
                const canvasJSON = fabricCanvas.toJSON(['selectable', 'evented', 'id', 'name']);

                return this.processFallbackExportResult(fabricCanvas, highQualityImage, canvasJSON);
            }

        } catch (error) {
            console.error('Failed to capture Fabric.js canvas data:', error);

            // Final fallback to standard quality with clean export
            try {
                console.warn('Using final fallback with clean canvas export');

                // Clean the canvas before final fallback export
                const cleanupState = this.prepareCanvasForCleanExport(fabricCanvas);

                try {
                    const standardImage = fabricCanvas.toDataURL({
                        format: 'png',
                        quality: 0.9,
                        multiplier: 2
                    });

                    return {
                        imageData: standardImage,
                        canvasJSON: fabricCanvas.toJSON(),
                        dimensions: {
                            width: fabricCanvas.width,
                            height: fabricCanvas.height,
                            zoom: fabricCanvas.getZoom()
                        },
                        captureMethod: 'fabric_final_fallback_clean',
                        cleanExport: true,
                        timestamp: new Date().toISOString()
                    };
                } finally {
                    // Always restore canvas state
                    this.restoreCanvasAfterExport(fabricCanvas, cleanupState);
                }
            } catch (fallbackError) {
                console.error('Fabric.js fallback capture also failed:', fallbackError);
                throw fallbackError;
            }
        }
    }

    /**
     * Basic canvas capture fallback with CORS-safe options
     */
    async basicCanvasCapture(canvas) {
        try {
            console.log('🔄 Attempting basic canvas capture...');

            // First try: If it's an HTML canvas element, use toDataURL directly
            if (canvas && canvas.tagName === 'CANVAS' && canvas.toDataURL) {
                console.log('✅ Using HTML canvas toDataURL method');
                return canvas.toDataURL('image/png', 1.0);
            }

            // Second try: Use html-to-image library if available
            if (window.htmlToImage) {
                console.log('🔄 Using html-to-image library...');
                const dataUrl = await window.htmlToImage.toPng(canvas, {
                    quality: 0.98,
                    pixelRatio: 4,
                    backgroundColor: 'transparent',
                    // CORS-safe options to avoid font loading issues
                    skipFonts: true,
                    fontEmbedCSS: false,
                    useCORS: false,
                    allowTaint: true,
                    includeQueryParams: false,
                    // Filter out problematic elements
                    filter: (node) => {
                        // Skip any external font links or stylesheets
                        if (node.tagName === 'LINK' && node.href && node.href.includes('fonts.googleapis.com')) {
                            return false;
                        }
                        return true;
                    }
                });
                console.log('✅ html-to-image capture successful');
                return dataUrl;
            }

            // Third try: Create a new canvas and draw the content
            if (canvas && canvas.tagName === 'CANVAS') {
                console.log('🔄 Creating new canvas for capture...');
                const newCanvas = document.createElement('canvas');
                newCanvas.width = canvas.width || 800;
                newCanvas.height = canvas.height || 400;
                const ctx = newCanvas.getContext('2d');

                // Fill with white background
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, newCanvas.width, newCanvas.height);

                // Try to draw the original canvas
                try {
                    ctx.drawImage(canvas, 0, 0);
                    console.log('✅ Canvas content copied successfully');
                    return newCanvas.toDataURL('image/png', 1.0);
                } catch (drawError) {
                    console.warn('Failed to draw canvas content:', drawError);
                    // Return the blank canvas with white background
                    return newCanvas.toDataURL('image/png', 1.0);
                }
            }

            // Final fallback - create a placeholder image
            console.warn('⚠️ Creating placeholder image as final fallback');
            const placeholderCanvas = document.createElement('canvas');
            placeholderCanvas.width = 800;
            placeholderCanvas.height = 400;
            const ctx = placeholderCanvas.getContext('2d');

            // Create a simple placeholder
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, 800, 400);
            ctx.fillStyle = '#6c757d';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Billboard Design', 400, 200);

            return placeholderCanvas.toDataURL('image/png', 1.0);

        } catch (error) {
            console.error('All canvas capture methods failed:', error);

            // Emergency fallback - create minimal placeholder
            const emergencyCanvas = document.createElement('canvas');
            emergencyCanvas.width = 800;
            emergencyCanvas.height = 400;
            const ctx = emergencyCanvas.getContext('2d');
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, 800, 400);

            return emergencyCanvas.toDataURL('image/png', 1.0);
        }
    }

    /**
     * Prepare canvas for clean export by removing UI elements
     */
    prepareCanvasForCleanExport(canvas) {
        const cleanupState = {
            activeObject: canvas.getActiveObject(),
            activeSelection: canvas.getActiveSelection(),
            interactive: canvas.interactive,
            selection: canvas.selection,
            hoverCursor: canvas.hoverCursor,
            moveCursor: canvas.moveCursor,
            defaultCursor: canvas.defaultCursor,
            objectsState: []
        };

        // Store original object states and disable interactivity
        canvas.forEachObject((obj) => {
            const objState = {
                object: obj,
                selectable: obj.selectable,
                evented: obj.evented,
                hasControls: obj.hasControls,
                hasBorders: obj.hasBorders,
                hoverCursor: obj.hoverCursor,
                moveCursor: obj.moveCursor
            };
            cleanupState.objectsState.push(objState);

            // Disable all interactive features
            obj.selectable = false;
            obj.evented = false;
            obj.hasControls = false;
            obj.hasBorders = false;
            obj.hoverCursor = 'default';
            obj.moveCursor = 'default';
        });

        // Disable canvas interactivity
        canvas.interactive = false;
        canvas.selection = false;
        canvas.hoverCursor = 'default';
        canvas.moveCursor = 'default';
        canvas.defaultCursor = 'default';

        // Clear any active selections
        canvas.discardActiveObject();

        // Force canvas re-render to apply changes
        canvas.renderAll();

        console.log('✅ Canvas prepared for clean export (UI elements disabled)');
        return cleanupState;
    }

    /**
     * Restore canvas state after export
     */
    restoreCanvasAfterExport(canvas, cleanupState) {
        try {
            // Restore canvas properties
            canvas.interactive = cleanupState.interactive;
            canvas.selection = cleanupState.selection;
            canvas.hoverCursor = cleanupState.hoverCursor;
            canvas.moveCursor = cleanupState.moveCursor;
            canvas.defaultCursor = cleanupState.defaultCursor;

            // Restore object states
            cleanupState.objectsState.forEach((objState) => {
                const obj = objState.object;
                obj.selectable = objState.selectable;
                obj.evented = objState.evented;
                obj.hasControls = objState.hasControls;
                obj.hasBorders = objState.hasBorders;
                obj.hoverCursor = objState.hoverCursor;
                obj.moveCursor = objState.moveCursor;
            });

            // Restore active selection if it existed
            if (cleanupState.activeObject) {
                canvas.setActiveObject(cleanupState.activeObject);
            }

            // Force canvas re-render to apply restored state
            canvas.renderAll();

            console.log('✅ Canvas state restored after export');

        } catch (error) {
            console.error('Error restoring canvas state:', error);
            // Force a basic restore
            canvas.interactive = true;
            canvas.selection = true;
            canvas.forEachObject((obj) => {
                obj.selectable = true;
                obj.evented = true;
                obj.hasControls = true;
                obj.hasBorders = true;
            });
            canvas.renderAll();
        }
    }

    /**
     * Process fallback export result
     */
    processFallbackExportResult(canvas, imageData, canvasJSON) {
        // Get canvas dimensions
        const canvasDimensions = {
            width: canvas.width,
            height: canvas.height,
            zoom: canvas.getZoom()
        };

        return {
            imageData: imageData,
            canvasJSON: canvasJSON,
            dimensions: canvasDimensions,
            captureMethod: 'fabric_fallback_clean',
            qualityLevel: 'high',
            cleanExport: true, // Flag indicating UI elements were removed
            timestamp: new Date().toISOString()
        };
    }
}

// Global functions for HTML onclick handlers
async function openUnifiedCheckout() {
    if (window.unifiedCheckout) {
        await window.unifiedCheckout.open();
    }
}

function closeUnifiedCheckout() {
    if (window.unifiedCheckout) {
        window.unifiedCheckout.close();
    }
}

function proceedToPayment() {
    if (window.unifiedCheckout) {
        window.unifiedCheckout.proceedToPayment();
    }
}

function openTermsModal() {
    const modal = document.getElementById('termsModal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

function closeTermsModal() {
    const modal = document.getElementById('termsModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 Checkout modal script loaded');
    if (!window.unifiedCheckout) {
        window.unifiedCheckout = new UnifiedCheckout();
        console.log('✅ UnifiedCheckout instance created from checkout-modal.js');
    } else {
        console.log('✅ UnifiedCheckout instance already exists');
    }
});
