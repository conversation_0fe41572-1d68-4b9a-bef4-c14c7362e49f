/**
 * Fabric.js Canvas Exporter
 * High-quality export utility for Fabric.js canvases with proper error handling and fallbacks
 */

class FabricCanvasExporter {
    constructor() {
        this.isExporting = false;
        this.lastExportResult = null;
        
        // 🔥 UPGRADED: Ultra high-quality export settings for both mobile and desktop
        this.exportSettings = {
            // Ultra high quality settings for checkout (targeting 3200x1600+ resolution)
            highQuality: {
                format: 'png',
                quality: 1.0,
                multiplier: 8, // 🔥 INCREASED to 8x resolution (800x400 -> 6400x3200)
                enableRetinaScaling: true
            },

            // High quality for faster processing but still excellent resolution
            standardQuality: {
                format: 'png',
                quality: 0.98,
                multiplier: 6, // 🔥 INCREASED to 6x (800x400 -> 4800x2400)
                enableRetinaScaling: true
            },

            // Fallback quality for compatibility (still better than before)
            fallbackQuality: {
                format: 'png',
                quality: 0.95,
                multiplier: 4, // 🔥 INCREASED to 4x (800x400 -> 3200x1600)
                enableRetinaScaling: true
            }
        };
    }
    
    /**
     * Find active Fabric.js canvas instance
     */
    findFabricCanvas() {
        // Check for global canvas instances
        if (window.canvas && window.canvas.toDataURL) {
            return window.canvas;
        }

        // Check for canvas in CF7 editors
        if (window.cf7Editors && window.cf7Editors.length > 0) {
            for (const editor of window.cf7Editors) {
                if (editor.canvas && editor.canvas.toDataURL) {
                    return editor.canvas;
                }
            }
        }

        // Check for canvas in billboard editor modules
        if (window.billboardEditor && window.billboardEditor.modules) {
            const canvasManager = window.billboardEditor.modules.canvasManager;
            if (canvasManager && canvasManager.getCanvas) {
                const canvas = canvasManager.getCanvas();
                if (canvas && canvas.toDataURL) {
                    return canvas;
                }
            }
        }

        // Check for templated billboard canvas
        if (window.canvasManager && window.canvasManager.canvas) {
            return window.canvasManager.canvas;
        }

        // Check all Fabric.js instances and match with canvas elements
        if (window.fabric && window.fabric.Canvas && window.fabric.Canvas.getInstances) {
            const fabricInstances = window.fabric.Canvas.getInstances();
            console.log(`🔍 Fabric Exporter: Found ${fabricInstances.length} Fabric.js instances`);

            // Check common canvas IDs
            const canvasIds = ['fabricCanvas', 'billboard-canvas', 'canvas'];

            for (const canvasId of canvasIds) {
                const canvasElement = document.getElementById(canvasId);
                if (canvasElement) {
                    console.log(`🔍 Fabric Exporter: Found canvas element with ID: ${canvasId}`);

                    // Find matching Fabric instance
                    for (let i = 0; i < fabricInstances.length; i++) {
                        const fabricCanvas = fabricInstances[i];
                        if (fabricCanvas.lowerCanvasEl === canvasElement ||
                            fabricCanvas.upperCanvasEl === canvasElement ||
                            fabricCanvas.getElement() === canvasElement) {
                            console.log(`✅ Fabric Exporter: Found matching Fabric.js canvas for ${canvasId}`);
                            return fabricCanvas;
                        }
                    }
                }
            }

            // If no specific match, return the first available Fabric instance
            if (fabricInstances.length > 0) {
                console.log('✅ Fabric Exporter: Using first available Fabric.js instance');
                return fabricInstances[0];
            }
        }

        return null;
    }
    
    /**
     * Export canvas with high quality settings
     */
    async exportHighQuality(canvas = null, options = {}) {
        if (this.isExporting) {
            throw new Error('Export already in progress');
        }
        
        try {
            this.isExporting = true;
            
            // Find canvas if not provided
            if (!canvas) {
                canvas = this.findFabricCanvas();
                if (!canvas) {
                    throw new Error('No Fabric.js canvas found');
                }
            }
            
            // Validate canvas
            if (!canvas.toDataURL) {
                throw new Error('Invalid canvas object - missing toDataURL method');
            }
            
            console.log('Starting high-quality Fabric.js canvas export...');
            
            // Try high quality first
            let exportResult = await this.tryExportWithSettings(canvas, 'highQuality', options);
            
            if (exportResult.success) {
                this.lastExportResult = exportResult;
                return exportResult;
            }
            
            // Fallback to standard quality
            console.warn('High quality export failed, trying standard quality...');
            exportResult = await this.tryExportWithSettings(canvas, 'standardQuality', options);
            
            if (exportResult.success) {
                this.lastExportResult = exportResult;
                return exportResult;
            }
            
            // Final fallback
            console.warn('Standard quality export failed, trying fallback quality...');
            exportResult = await this.tryExportWithSettings(canvas, 'fallbackQuality', options);
            
            this.lastExportResult = exportResult;
            return exportResult;
            
        } finally {
            this.isExporting = false;
        }
    }
    
    /**
     * Try export with specific quality settings
     */
    async tryExportWithSettings(canvas, qualityLevel, userOptions = {}) {
        try {
            const settings = { ...this.exportSettings[qualityLevel], ...userOptions };

            console.log(`🎯 Attempting SUPER HIGH RESOLUTION export with ${qualityLevel} settings:`, settings);
            console.log(`📐 Canvas current dimensions: ${canvas.width}x${canvas.height}`);
            console.log(`🔥 Target output resolution: ${canvas.width * settings.multiplier}x${canvas.height * settings.multiplier}`);

            // 🔥 CRITICAL: Ensure canvas is at maximum base dimensions before export
            const originalWidth = canvas.width;
            const originalHeight = canvas.height;

            // Force canvas to use maximum base dimensions if it's smaller
            if (canvas.width < 800 || canvas.height < 400) {
                console.log(`⚠️ Canvas too small (${canvas.width}x${canvas.height}), forcing to 800x400`);
                canvas.setDimensions({ width: 800, height: 400 });
                canvas.renderAll();
            }

            // Clean the canvas before export (remove UI elements)
            const cleanupState = this.prepareCanvasForCleanExport(canvas);

            try {
                // Export image data (now without UI elements)
                const imageData = canvas.toDataURL(settings);

                // Validate image data
                if (!imageData || !imageData.startsWith('data:image/')) {
                    throw new Error('Invalid image data generated');
                }

                // 🔥 VERIFY actual output resolution
                const img = new Image();
                img.onload = function() {
                    console.log(`✅ ACTUAL OUTPUT RESOLUTION: ${img.width}x${img.height} pixels`);
                    console.log(`🎯 Expected: ${canvas.width * settings.multiplier}x${canvas.height * settings.multiplier}`);
                    console.log(`📊 Quality achieved: ${img.width >= 3200 ? 'SUPER HIGH ✅' : 'NEEDS IMPROVEMENT ⚠️'}`);
                };
                img.src = imageData;

                return await this.processExportResult(canvas, imageData, qualityLevel, settings);

            } finally {
                // Always restore canvas state
                this.restoreCanvasAfterExport(canvas, cleanupState);
            }

        } catch (error) {
            console.error(`Export failed with ${qualityLevel}:`, error);
            return {
                success: false,
                error: error.message,
                qualityLevel: qualityLevel
            };
        }
    }
    
    /**
     * Export for checkout (optimized for payment processing)
     */
    async exportForCheckout(canvas = null) {
        try {
            // 🔥 ULTRA HIGH RESOLUTION EXPORT - Bypass all scaling issues
            console.log('🚀 Starting ULTRA HIGH RESOLUTION checkout export...');
            const result = await this.exportUltraHighResolution(canvas);

            if (!result.success) {
                console.warn('Ultra high resolution failed, trying super high resolution');
                const fallbackResult = await this.exportSuperHighResolution(canvas);

                if (!fallbackResult.success) {
                    console.warn('Super high resolution failed, falling back to standard high quality');
                    return await this.exportHighQuality(canvas, {
                        multiplier: 8,
                        quality: 1.0
                    });
                }

                return fallbackResult;
            }

            return result;
            
        } catch (error) {
            console.error('Checkout export failed:', error);
            throw error;
        }
    }

    /**
     * 🔥 SUPER HIGH RESOLUTION EXPORT - Force maximum quality and resolution
     */
    async exportSuperHighResolution(canvas = null) {
        try {
            console.log('🚀 Starting SUPER HIGH RESOLUTION export...');

            // Find canvas if not provided
            if (!canvas) {
                canvas = this.findFabricCanvas();
                if (!canvas) {
                    throw new Error('No Fabric.js canvas found');
                }
            }

            // 🔥 FORCE MAXIMUM BASE DIMENSIONS
            const targetBaseWidth = 1600;  // 🔥 DOUBLED base width for super quality
            const targetBaseHeight = 800;  // 🔥 DOUBLED base height for super quality
            const superMultiplier = 4;     // 🔥 4x on top of doubled base = 6400x3200 final

            console.log(`📐 Current canvas: ${canvas.width}x${canvas.height}`);
            console.log(`🎯 Target base: ${targetBaseWidth}x${targetBaseHeight}`);
            console.log(`🔥 Final target: ${targetBaseWidth * superMultiplier}x${targetBaseHeight * superMultiplier}`);

            // Store original dimensions
            const originalWidth = canvas.width;
            const originalHeight = canvas.height;
            const originalZoom = canvas.getZoom();

            try {
                // 🔥 STEP 1: Scale up canvas to maximum base dimensions
                canvas.setDimensions({
                    width: targetBaseWidth,
                    height: targetBaseHeight
                });

                // 🔥 STEP 2: Scale all objects proportionally
                const scaleX = targetBaseWidth / originalWidth;
                const scaleY = targetBaseHeight / originalHeight;

                canvas.getObjects().forEach(obj => {
                    obj.scaleX = (obj.scaleX || 1) * scaleX;
                    obj.scaleY = (obj.scaleY || 1) * scaleY;
                    obj.left = obj.left * scaleX;
                    obj.top = obj.top * scaleY;
                    obj.setCoords();
                });

                canvas.renderAll();

                // 🔥 STEP 3: Export at super high quality
                const imageData = canvas.toDataURL({
                    format: 'png',
                    quality: 1.0,
                    multiplier: superMultiplier,
                    enableRetinaScaling: true
                });

                // 🔥 VERIFY actual output resolution
                const img = new Image();
                img.onload = function() {
                    console.log(`🎉 SUPER HIGH RESOLUTION ACHIEVED: ${img.width}x${img.height} pixels`);
                    const fileSizeKB = Math.round(imageData.length * 0.75 / 1024);
                    console.log(`📊 File size: ${fileSizeKB}KB (${(fileSizeKB/1024).toFixed(2)}MB)`);
                };
                img.src = imageData;

                // Capture canvas JSON for reconstruction
                const canvasJSON = canvas.toJSON(['selectable', 'evented', 'id', 'name']);

                return {
                    success: true,
                    imageData: imageData,
                    canvasJSON: canvasJSON,
                    dimensions: {
                        width: targetBaseWidth,
                        height: targetBaseHeight,
                        outputWidth: targetBaseWidth * superMultiplier,
                        outputHeight: targetBaseHeight * superMultiplier
                    },
                    captureMethod: 'fabric_super_high_resolution',
                    qualityLevel: 'super_high',
                    fileSize: Math.round(imageData.length * 0.75),
                    timestamp: new Date().toISOString()
                };

            } finally {
                // 🔥 RESTORE original canvas state
                canvas.setDimensions({
                    width: originalWidth,
                    height: originalHeight
                });

                // Restore object positions and scales
                const restoreScaleX = originalWidth / targetBaseWidth;
                const restoreScaleY = originalHeight / targetBaseHeight;

                canvas.getObjects().forEach(obj => {
                    obj.scaleX = (obj.scaleX || 1) * restoreScaleX;
                    obj.scaleY = (obj.scaleY || 1) * restoreScaleY;
                    obj.left = obj.left * restoreScaleX;
                    obj.top = obj.top * restoreScaleY;
                    obj.setCoords();
                });

                canvas.setZoom(originalZoom);
                canvas.renderAll();
                console.log('✅ Canvas state restored after super high resolution export');
            }

        } catch (error) {
            console.error('❌ Super high resolution export failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 🔥 ULTRA HIGH RESOLUTION EXPORT - Creates new canvas at maximum resolution
     * This bypasses ALL responsive scaling and CSS transform issues
     */
    async exportUltraHighResolution(canvas = null) {
        try {
            console.log('🚀 Starting ULTRA HIGH RESOLUTION export (bypassing all scaling)...');

            // Find canvas if not provided
            if (!canvas) {
                canvas = this.findFabricCanvas();
                if (!canvas) {
                    throw new Error('No Fabric.js canvas found');
                }
            }

            // 🔥 ULTRA HIGH RESOLUTION SETTINGS
            const ultraWidth = 3200;   // 🔥 ULTRA HIGH base width
            const ultraHeight = 1600;  // 🔥 ULTRA HIGH base height
            const ultraMultiplier = 3; // 🔥 3x multiplier = 9600x4800 final resolution

            console.log(`🎯 ULTRA HIGH RESOLUTION TARGET: ${ultraWidth * ultraMultiplier}x${ultraHeight * ultraMultiplier} pixels`);

            // 🔥 CREATE COMPLETELY NEW CANVAS at ultra high resolution
            const tempCanvasElement = document.createElement('canvas');
            tempCanvasElement.width = ultraWidth;
            tempCanvasElement.height = ultraHeight;
            tempCanvasElement.style.position = 'absolute';
            tempCanvasElement.style.left = '-9999px';
            tempCanvasElement.style.top = '-9999px';
            document.body.appendChild(tempCanvasElement);

            const ultraCanvas = new fabric.Canvas(tempCanvasElement, {
                width: ultraWidth,
                height: ultraHeight,
                enableRetinaScaling: false // We control the resolution manually
            });

            try {
                // 🔥 COPY ALL OBJECTS from original canvas to ultra high resolution canvas
                const canvasData = canvas.toJSON(['selectable', 'evented', 'id', 'name']);

                // Calculate scaling factors
                const scaleX = ultraWidth / canvas.width;
                const scaleY = ultraHeight / canvas.height;

                console.log(`📐 Scaling objects: ${scaleX.toFixed(2)}x horizontal, ${scaleY.toFixed(2)}x vertical`);

                // Load the canvas data into ultra high resolution canvas
                await new Promise((resolve, reject) => {
                    ultraCanvas.loadFromJSON(canvasData, () => {
                        // Scale all objects to ultra high resolution
                        ultraCanvas.getObjects().forEach(obj => {
                            obj.scaleX = (obj.scaleX || 1) * scaleX;
                            obj.scaleY = (obj.scaleY || 1) * scaleY;
                            obj.left = obj.left * scaleX;
                            obj.top = obj.top * scaleY;
                            obj.setCoords();
                        });

                        ultraCanvas.renderAll();
                        resolve();
                    }, reject);
                });

                // 🔥 EXPORT at ultra high quality
                const imageData = ultraCanvas.toDataURL({
                    format: 'png',
                    quality: 1.0,
                    multiplier: ultraMultiplier,
                    enableRetinaScaling: true
                });

                // 🔥 VERIFY actual output resolution
                const img = new Image();
                img.onload = function() {
                    console.log(`🎉 ULTRA HIGH RESOLUTION ACHIEVED: ${img.width}x${img.height} pixels`);
                    const fileSizeKB = Math.round(imageData.length * 0.75 / 1024);
                    const fileSizeMB = (fileSizeKB / 1024).toFixed(2);
                    console.log(`📊 File size: ${fileSizeKB}KB (${fileSizeMB}MB)`);
                    console.log(`🏆 Quality level: ${img.width >= 6400 ? 'ULTRA HIGH ✅' : img.width >= 3200 ? 'HIGH ✅' : 'STANDARD ⚠️'}`);
                };
                img.src = imageData;

                return {
                    success: true,
                    imageData: imageData,
                    canvasJSON: canvasData,
                    dimensions: {
                        width: ultraWidth,
                        height: ultraHeight,
                        outputWidth: ultraWidth * ultraMultiplier,
                        outputHeight: ultraHeight * ultraMultiplier
                    },
                    captureMethod: 'fabric_ultra_high_resolution',
                    qualityLevel: 'ultra_high',
                    fileSize: Math.round(imageData.length * 0.75),
                    timestamp: new Date().toISOString()
                };

            } finally {
                // 🔥 CLEANUP: Remove temporary canvas
                ultraCanvas.dispose();
                document.body.removeChild(tempCanvasElement);
                console.log('✅ Temporary ultra high resolution canvas cleaned up');
            }

        } catch (error) {
            console.error('❌ Ultra high resolution export failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Export for download (maximum quality)
     */
    async exportForDownload(canvas = null, filename = 'billboard-design') {
        try {
            const result = await this.exportHighQuality(canvas);
            
            if (result.success) {
                // Create download link
                const link = document.createElement('a');
                link.download = `${filename}.png`;
                link.href = result.imageData;
                
                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                return result;
            } else {
                throw new Error(result.error || 'Export failed');
            }
            
        } catch (error) {
            console.error('Download export failed:', error);
            throw error;
        }
    }
    
    /**
     * Get canvas information without exporting
     */
    getCanvasInfo(canvas = null) {
        if (!canvas) {
            canvas = this.findFabricCanvas();
        }
        
        if (!canvas) {
            return null;
        }
        
        return {
            width: canvas.width,
            height: canvas.height,
            zoom: canvas.getZoom(),
            objectCount: canvas.getObjects().length,
            hasBackground: !!canvas.backgroundImage,
            backgroundColor: canvas.backgroundColor
        };
    }
    
    /**
     * Check if export is currently in progress
     */
    isExportInProgress() {
        return this.isExporting;
    }
    
    /**
     * Get last export result
     */
    getLastExportResult() {
        return this.lastExportResult;
    }

    /**
     * Prepare canvas for clean export by removing UI elements
     */
    prepareCanvasForCleanExport(canvas) {
        const cleanupState = {
            activeObject: canvas.getActiveObject(),
            activeSelection: canvas.getActiveSelection(),
            interactive: canvas.interactive,
            selection: canvas.selection,
            hoverCursor: canvas.hoverCursor,
            moveCursor: canvas.moveCursor,
            defaultCursor: canvas.defaultCursor,
            objectsState: []
        };

        // Store original object states and disable interactivity
        canvas.forEachObject((obj) => {
            const objState = {
                object: obj,
                selectable: obj.selectable,
                evented: obj.evented,
                hasControls: obj.hasControls,
                hasBorders: obj.hasBorders,
                hoverCursor: obj.hoverCursor,
                moveCursor: obj.moveCursor
            };
            cleanupState.objectsState.push(objState);

            // Disable all interactive features
            obj.selectable = false;
            obj.evented = false;
            obj.hasControls = false;
            obj.hasBorders = false;
            obj.hoverCursor = 'default';
            obj.moveCursor = 'default';
        });

        // Disable canvas interactivity
        canvas.interactive = false;
        canvas.selection = false;
        canvas.hoverCursor = 'default';
        canvas.moveCursor = 'default';
        canvas.defaultCursor = 'default';

        // Clear any active selections
        canvas.discardActiveObject();

        // Force canvas re-render to apply changes
        canvas.renderAll();

        console.log('✅ Canvas prepared for clean export (UI elements disabled)');
        return cleanupState;
    }

    /**
     * Restore canvas state after export
     */
    restoreCanvasAfterExport(canvas, cleanupState) {
        try {
            // Restore canvas properties
            canvas.interactive = cleanupState.interactive;
            canvas.selection = cleanupState.selection;
            canvas.hoverCursor = cleanupState.hoverCursor;
            canvas.moveCursor = cleanupState.moveCursor;
            canvas.defaultCursor = cleanupState.defaultCursor;

            // Restore object states
            cleanupState.objectsState.forEach((objState) => {
                const obj = objState.object;
                obj.selectable = objState.selectable;
                obj.evented = objState.evented;
                obj.hasControls = objState.hasControls;
                obj.hasBorders = objState.hasBorders;
                obj.hoverCursor = objState.hoverCursor;
                obj.moveCursor = objState.moveCursor;
            });

            // Restore active selection if it existed
            if (cleanupState.activeObject) {
                canvas.setActiveObject(cleanupState.activeObject);
            }

            // Force canvas re-render to apply restored state
            canvas.renderAll();

            console.log('✅ Canvas state restored after export');

        } catch (error) {
            console.error('Error restoring canvas state:', error);
            // Force a basic restore
            canvas.interactive = true;
            canvas.selection = true;
            canvas.forEachObject((obj) => {
                obj.selectable = true;
                obj.evented = true;
                obj.hasControls = true;
                obj.hasBorders = true;
            });
            canvas.renderAll();
        }
    }

    /**
     * Process export result with validation and metadata
     */
    async processExportResult(canvas, imageData, qualityLevel, settings) {
        // Check file size (approximate)
        const sizeInBytes = (imageData.length * 0.75); // Base64 to binary approximation
        const sizeInMB = sizeInBytes / (1024 * 1024);

        console.log(`✅ Clean export successful - Size: ${sizeInMB.toFixed(2)}MB (no UI elements)`);

        // Also capture canvas JSON for reconstruction (with clean state)
        const canvasJSON = canvas.toJSON(['selectable', 'evented', 'id', 'name']);

        return {
            success: true,
            imageData: imageData,
            canvasJSON: canvasJSON,
            dimensions: {
                width: canvas.getWidth(),
                height: canvas.getHeight()
            },
            fileSize: {
                bytes: sizeInBytes,
                mb: sizeInMB
            },
            qualityLevel: qualityLevel,
            settings: settings,
            cleanExport: true, // Flag indicating UI elements were removed
            timestamp: new Date().toISOString()
        };
    }
}

// Create global instance
window.fabricCanvasExporter = new FabricCanvasExporter();

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FabricCanvasExporter;
}
