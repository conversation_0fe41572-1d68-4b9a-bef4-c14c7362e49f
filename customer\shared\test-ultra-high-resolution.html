<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra High Resolution Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .canvas-container {
            border: 2px solid #ddd;
            margin: 20px 0;
            display: flex;
            justify-content: center;
            padding: 20px;
            background: #fafafa;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .results {
            margin: 20px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Ultra High Resolution Export Test</h1>
        <p>This test verifies that the new ultra high resolution export system works correctly.</p>
        
        <div class="canvas-container">
            <canvas id="testCanvas" width="800" height="400" style="border: 2px solid #000;"></canvas>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="addTestContent()">Add Test Content</button>
            <button class="btn" onclick="testStandardExport()">Test Standard Export</button>
            <button class="btn" onclick="testUltraHighExport()">Test Ultra High Export</button>
            <button class="btn" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="results" class="results">
Ready to test ultra high resolution export...
        </div>
    </div>

    <script src="fabric-canvas-exporter.js"></script>
    <script>
        let canvas;
        let exporter;
        
        // Initialize canvas
        document.addEventListener('DOMContentLoaded', function() {
            canvas = new fabric.Canvas('testCanvas', {
                width: 800,
                height: 400,
                backgroundColor: '#ffffff'
            });
            
            exporter = new FabricCanvasExporter();
            
            addTestContent();
            log('✅ Test canvas initialized', 'success');
        });
        
        function addTestContent() {
            canvas.clear();
            canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
            
            // Add some test content
            const text = new fabric.Text('ULTRA HIGH RESOLUTION TEST', {
                left: 50,
                top: 50,
                fontSize: 32,
                fill: '#333',
                fontWeight: 'bold'
            });
            
            const rect = new fabric.Rect({
                left: 50,
                top: 120,
                width: 200,
                height: 100,
                fill: '#007bff',
                stroke: '#0056b3',
                strokeWidth: 3
            });
            
            const circle = new fabric.Circle({
                left: 300,
                top: 120,
                radius: 50,
                fill: '#28a745',
                stroke: '#1e7e34',
                strokeWidth: 3
            });
            
            const text2 = new fabric.Text('Resolution: ' + canvas.width + 'x' + canvas.height, {
                left: 50,
                top: 250,
                fontSize: 18,
                fill: '#666'
            });
            
            canvas.add(text, rect, circle, text2);
            canvas.renderAll();
            
            log('✅ Test content added to canvas', 'success');
        }
        
        async function testStandardExport() {
            try {
                log('🔄 Testing standard export...', '');
                
                const dataURL = canvas.toDataURL({
                    format: 'png',
                    quality: 1.0,
                    multiplier: 2
                });
                
                const img = new Image();
                img.onload = function() {
                    log(`📊 Standard Export Result:
   Canvas size: ${canvas.width}x${canvas.height}
   Multiplier: 2x
   Output resolution: ${img.width}x${img.height}
   File size: ~${Math.round(dataURL.length * 0.75 / 1024)}KB`, 'warning');
                };
                img.src = dataURL;
                
            } catch (error) {
                log('❌ Standard export failed: ' + error.message, 'error');
            }
        }
        
        async function testUltraHighExport() {
            try {
                log('🚀 Testing ULTRA HIGH RESOLUTION export...', '');
                
                const result = await exporter.exportUltraHighResolution(canvas);
                
                if (result.success) {
                    const img = new Image();
                    img.onload = function() {
                        const fileSizeKB = Math.round(result.imageData.length * 0.75 / 1024);
                        const fileSizeMB = (fileSizeKB / 1024).toFixed(2);
                        
                        log(`🎉 ULTRA HIGH RESOLUTION SUCCESS:
   Original canvas: ${canvas.width}x${canvas.height}
   Ultra base: ${result.dimensions.width}x${result.dimensions.height}
   Final output: ${img.width}x${img.height}
   Target was: ${result.dimensions.outputWidth}x${result.dimensions.outputHeight}
   File size: ${fileSizeKB}KB (${fileSizeMB}MB)
   Quality level: ${img.width >= 6400 ? 'ULTRA HIGH ✅' : img.width >= 3200 ? 'HIGH ✅' : 'STANDARD ⚠️'}`, 'success');
                    };
                    img.src = result.imageData;
                } else {
                    log('❌ Ultra high resolution export failed: ' + result.error, 'error');
                }
                
            } catch (error) {
                log('❌ Ultra high resolution export error: ' + error.message, 'error');
            }
        }
        
        function log(message, type = '') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            results.textContent += `[${timestamp}] ${message}\n`;
            
            if (type) {
                results.className = 'results ' + type;
            }
            
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            const results = document.getElementById('results');
            results.textContent = 'Results cleared...\n';
            results.className = 'results';
        }
    </script>
</body>
</html>
